import { z } from 'zod';
import { baseEntitySchema, idSchema } from '../common/base.schema';
import { tagSchema } from '../tag/tag.schema';
import { employeeSchema } from '../organization/employee.schema';
import { taskStatusNameSchema, taskTypeSchema } from '../common/enums.schema';
import { taskPrioritySchema } from './task-priority.schema';
import { taskSizeSchema } from './task-size.schema';
import { taskStatusSchema } from './task-status.schema';
import { organizationTeamSchema } from '../team/organization-team.schema';

export const basePerTenantAndOrganizationEntitySchema = baseEntitySchema.extend({
	tenantId: idSchema.optional(),
	organizationId: idSchema.optional()
});

// schema for ITaskSize
export const taskSizeEntitySchema = basePerTenantAndOrganizationEntitySchema.extend({
	name: z.string(),
	value: z.string(),
	description: z.string().optional(),
	icon: z.string().optional(),
	color: z.string().optional(),
	isSystem: z.boolean().optional(),
	template: z.string().optional()
});

// schema for ITaskPriority
export const taskPriorityEntitySchema = basePerTenantAndOrganizationEntitySchema.extend({
	name: z.string(),
	value: z.string(),
	description: z.string().optional(),
	icon: z.string().optional(),
	color: z.string().optional(),
	isSystem: z.boolean().optional(),
	template: z.string().optional()
});

// schema for IIssueType
export const issueTypeEntitySchema = basePerTenantAndOrganizationEntitySchema.extend({
	name: z.string(),
	value: z.string(),
	description: z.string().optional(),
	icon: z.string().optional(),
	color: z.string().optional(),
	isSystem: z.boolean().optional(),
	imageId: z.string().nullable().optional(),
	projectId: z.string().nullable().optional(),
	organizationTeamId: idSchema.optional(),
	image: z.string().nullable().optional(),
	fullIconUrl: z.string().optional(),
	template: z.string().optional()
});

// schema for ITaskLinkedIssue
export const taskLinkedIssueSchema = z.object({
	organizationId: z.string(),
	taskToId: z.string(),
	taskFromId: z.string(),
	action: z.number()
});

// schema for IBaseTaskProperties
export const baseTaskPropertiesSchema = basePerTenantAndOrganizationEntitySchema.extend({
	title: z.string(),
	number: z.number().optional(),
	public: z.boolean().nullable(),
	prefix: z.string().optional().nullable(),
	description: z.string().optional(),
	status: taskStatusNameSchema.optional(),
	priority: z.union([taskPrioritySchema, z.string()]).optional().nullable(),
	size: z.union([taskSizeSchema, z.string()]).optional().nullable(),
	issueType: taskTypeSchema.optional(),
	startDate: z.date().optional(),
	resolvedAt: z.date().optional(),
	dueDate: z.date().optional(),
	estimate: z.number().optional(),
	isDraft: z.boolean().optional(),
	isScreeningTask: z.boolean().optional(),
	version: z.string().optional().nullable()
});

// schema for ITaskAssociations
export const taskAssociationsSchema = z.object({
	tags: z.array(tagSchema).optional(),
	projectId: z.string().optional().nullable(),
	members: z.array(employeeSchema).optional(),
	teams: z.array(organizationTeamSchema).optional()
});
const baseTaskSchema = z.object({
	title: z.string(),
	number: z.number().optional(),
	public: z.boolean().nullable(),
	prefix: z.string().optional().nullable(),
	description: z.string().optional(),
	status: taskStatusNameSchema.optional(),
	priority: taskPrioritySchema.optional().nullable(),
	size: taskSizeSchema.optional().nullable(),
	issueType: z.string().optional().nullable(),
	startDate: z.date().optional().nullable(),
	resolvedAt: z.date().optional().nullable(),
	dueDate: z.date().optional().nullable(),
	estimate: z.number().optional(),
	isDraft: z.boolean().optional(),
	isScreeningTask: z.boolean().optional(),
	version: z.string().optional().nullable(),
	tags: z.array(tagSchema).optional().nullable(),
	projectId: z.string().optional().nullable(),
	members: z.array(z.any()).optional(),
	teams: z.array(organizationTeamSchema).optional(),

	parentId: z.string().optional().nullable(),
	children: z.array(z.any()).optional().nullable(),
	rootEpic: z.any().optional().nullable(),
	// Relations with the entities of status, size, priority and type
	taskStatus: taskStatusSchema.optional(),
	taskStatusId: z.string().optional().nullable(),
	taskSize: taskSizeEntitySchema.optional(),
	taskSizeId: z.string().optional().nullable(),
	taskPriority: taskPriorityEntitySchema.optional(),
	taskPriorityId: z.string().optional().nullable(),
	taskType: issueTypeEntitySchema.optional(),
	taskTypeId: z.string().optional().nullable(),

	// Additional properties specific to tasks
	taskNumber: z.string().optional(),
	totalWorkedTime: z.number().optional(),
	selectedTeam: organizationTeamSchema.optional(),
	linkedIssues: z.array(taskLinkedIssueSchema).optional(),
	label: z.string().optional(),
	estimateHours: z.number().optional(),
	estimateMinutes: z.number().optional()
});
// schema for ITask
export const taskSchema = baseTaskPropertiesSchema
	.merge(baseTaskSchema)
	// .merge(taskAssociationsSchema)
	.extend({
		// Relations with other tasks
		parent: baseTaskSchema.optional().nullable(),
		parentId: z.string().optional().nullable(),
		children: z.array(baseTaskSchema).optional().nullable(),
		rootEpic: baseTaskSchema.optional().nullable(),

		// Relations with the entities of status, size, priority and type
		taskStatus: taskStatusSchema.optional(),
		taskStatusId: z.string().optional().nullable(),
		taskSize: taskSizeEntitySchema.optional(),
		taskSizeId: z.string().optional().nullable(),
		taskPriority: taskPriorityEntitySchema.optional(),
		taskPriorityId: z.string().optional().nullable(),
		taskType: issueTypeEntitySchema.optional(),
		taskTypeId: z.string().optional().nullable(),

		// Additional properties specific to tasks
		taskNumber: z.string().optional(),
		totalWorkedTime: z.number().optional(),
		// selectedTeam: organizationTeamSchema.optional(),
		linkedIssues: z.array(taskLinkedIssueSchema).optional(),
		label: z.string().optional(),
		estimateHours: z.number().optional(),
		estimateMinutes: z.number().optional()
	});

// schema for ITasksStatistics
export const taskStatisticsSchema = taskSchema.extend({
	duration: z.number().optional(),
	durationPercentage: z.number().optional()
});

// schema for ICreateTask
export const createTaskSchema = z.object({
	title: z.string().min(1, 'Title is required'),
	status: z.string().optional(),
	size: z.string().optional(),
	priority: z.string().optional(),
	taskStatusId: z.string().optional(),
	issueType: z.string().optional(),
	members: z
		.array(
			z
				.object({
					id: z.string()
				})
				.passthrough()
		)
		.optional(),
	estimateDays: z.number().optional(),
	estimateHours: z.string().optional(),
	estimateMinutes: z.string().optional(),
	dueDate: z.string().optional(),
	description: z.string(),
	tags: z.array(z.object({ id: z.string() })),
	teams: z.array(z.object({ id: z.string() })),
	estimate: z.number(),
	organizationId: z.string(),
	tenantId: z.string(),
	projectId: z.string().nullable().optional()
});

export const updateActiveTaskSchema = z.object({
	affected: z.number(),
	generatedMaps: z.array(z.any()),
	raw: z.array(z.any())
});

// ===== TYPES TYPESCRIPT EXPORTED =====

export type TTask = z.infer<typeof taskSchema>;
export type TTaskStatistics = z.infer<typeof taskStatisticsSchema>;
export type TCreateTask = z.infer<typeof createTaskSchema>;
export type TEmployee = z.infer<typeof employeeSchema>;
export type TTag = z.infer<typeof tagSchema>;
export type TOrganizationTeam = z.infer<typeof organizationTeamSchema>;
export type TTaskStatus = z.infer<typeof taskStatusSchema>;
export type TTaskSize = z.infer<typeof taskSizeEntitySchema>;
export type TTaskPriority = z.infer<typeof taskPriorityEntitySchema>;
export type TIssueType = z.infer<typeof issueTypeEntitySchema>;
export type TTaskLinkedIssue = z.infer<typeof taskLinkedIssueSchema>;

// Types for enums
export type ETaskStatusName = z.infer<typeof taskStatusNameSchema>;
export type ETaskPriority = z.infer<typeof taskPrioritySchema>;
export type ETaskSize = z.infer<typeof taskSizeSchema>;
export type EIssueType = z.infer<typeof taskTypeSchema>;

// ===== UTILITIES FOR VALIDATION =====

/**
 * Validate a complete task
 */
export const validateTask = (data: unknown): TTask => {
	return taskSchema.parse(data);
};

/**
 * Secure validation of a task
 */
export const safeValidateTask = (data: unknown) => {
	return taskSchema.safeParse(data);
};

/**
 * Validate the data of a task creation
 */
export const validateCreateTask = (data: unknown): TCreateTask => {
	return createTaskSchema.parse(data);
};

/**
 * Partial validation for task updates
 */
export const validatePartialTask = (data: unknown) => {
	return taskSchema.partial().parse(data);
};

/**
 * Validate the statistics of a task
 */
export const validateTaskStatistics = (data: unknown): TTaskStatistics => {
	return taskStatisticsSchema.parse(data);
};

// ===== INTERFACE OF COMPATIBILITY (if necessary) =====

/**
 * Interface ITask for retrocompatibility
 * Use the type TTask instead
 */
export interface ITask extends z.infer<typeof taskSchema> {}

export default taskSchema;

("Validation failed in getTasks API response: items.3.tags.0.fullIconUrl: Expected object, received string, items.4.priority: Expected object, received string, items.4.size: Expected object, received string, items.4.children.0.priority: Expected object, received string, items.4.children.0.size: Expected object, received string, items.5.priority: Expected object, received string, items.5.size: Expected object, received string, items.5.tags.0.fullIconUrl: Expected object, received string, items.5.tags.1.fullIconUrl: Expected object, received string, items.5.parent.priority: Expected object, received string, items.5.parent.size: Expected object, received string, items.6.priority: Expected object, received string, items.6.size: Expected object, received string, items.6.startDate: Expected date, received string, items.6.dueDate: Expected date, received string, items.6.tags.0.fullIconUrl: Expected object, received string, items.6.tags.1.fullIconUrl: Expected object, received string, items.6.parent.priority: Expected object, received string, items.6.parent.size: Expected object, received string, items.7.priority: Expected object, received string, items.7.size: Expected object, received string, items.8.priority: Expected object, received string, items.8.size: Expected object, received string, items.9.priority: Expected object, received string, items.9.size: Expected object, received string, items.10.priority: Expected object, received string, items.10.size: Expected object, received string, items.11.priority: Expected object, received string, items.11.size: Expected object, received string, items.12.priority: Expected object, received string, items.12.size: Expected object, received string, items.12.tags.0.fullIconUrl: Expected object, received string, items.12.tags.1.fullIconUrl: Expected object, received string, items.12.children.0.priority: Expected object, received string, items.12.children.0.size: Expected object, received string, items.12.children.1.priority: Expected object, received string, items.12.children.1.size: Expected object, received string, items.13.priority: Expected object, received string, items.13.size: Expected object, received string, items.14.priority: Expected object, received string, items.14.size: Expected object, received string, items.14.children.0.priority: Expected object, received string, items.14.children.0.size: Expected object, received string, items.14.children.0.startDate: Expected date, received string, items.14.children.0.dueDate: Expected date, received string, items.14.children.1.priority: Expected object, received string, items.14.children.1.size: Expected object, received string, items.15.priority: Expected object, received string, items.15.size: Expected object, received string, items.16.priority: Expected object, received string, items.16.size: Expected object, received string, items.17.priority: Expected object, received string, items.17.size: Expected object, received string, items.18.priority: Expected object, received string, items.18.size: Expected object, received string, items.19.priority: Expected object, received string, items.19.size: Expected object, received string, items.19.tags.0.fullIconUrl: Expected object, received string, items.20.priority: Expected object, received string, items.20.size: Expected object, received string, items.21.priority: Expected object, received string, items.21.size: Expected object, received string, items.22.priority: Expected object, received string, items.22.size: Expected object, received string, items.23.priority: Expected object, received string, items.23.size: Expected object, received string, items.24.priority: Expected object, received string, items.24.size: Expected object, received string, items.25.priority: Expected object, received string, items.25.size: Expected object, received string, items.26.priority: Expected object, received string, items.26.size: Expected object, received string, items.26.tags.0.fullIconUrl: Expected object, received string, items.26.tags.1.fullIconUrl: Expected object, received string, items.27.priority: Expected object, received string, items.27.size: Expected object, received string, items.28.priority: Expected object, received string, items.28.size: Expected object, received string, items.29.priority: Expected object, received string, items.29.size: Expected object, received string, items.30.priority: Expected object, received string, items.30.size: Expected object, received string, items.31.priority: Expected object, received string, items.31.size: Expected object, received string, items.31.parent.priority: Expected object, received string, items.31.parent.size: Expected object, received string, items.32.priority: Expected object, received string, items.33.priority: Expected object, received string, items.33.size: Expected object, received string, items.34.priority: Expected object, received string, items.34.size: Expected object, received string, items.35.priority: Expected object, received string, items.35.size: Expected object, received string, items.36.priority: Expected object, received string, items.36.size: Expected object, received string, items.37.status: Invalid enum value. Expected 'blocked' | 'ready' | 'backlog' | 'todo' | 'in-progress' | 'completed' | 'closed' | 'in review' | 'open' | 'custom' | 'ready-for-review' | 'in-review' | 'done', received 'cancelled', items.37.priority: Expected object, received string, items.37.size: Expected object, received string, items.37.tags.0.fullIconUrl: Expected object, received string, items.37.tags.1.fullIconUrl: Expected object, received string, items.37.tags.2.fullIconUrl: Expected object, received string, items.38.priority: Expected object, received string, items.38.size: Expected object, received string, items.38.parent.priority: Expected object, received string, items.38.parent.size: Expected object, received string, items.39.priority: Expected object, received string, items.39.size: Expected object, received string, items.40.priority: Expected object, received string, items.40.size: Expected object, received string, items.41.priority: Expected object, received string, items.41.size: Expected object, received string, items.42.priority: Expected object, received string, items.42.size: Expected object, received string, items.42.tags.0.fullIconUrl: Expected object, received string, items.42.tags.1.fullIconUrl: Expected object, received string, items.42.tags.2.fullIconUrl: Expected object, received string, items.42.parent.priority: Expected object, received string, items.42.parent.size: Expected object, received string, items.43.priority: Expected object, received string, items.43.size: Expected object, received string, items.44.priority: Expected object, received string, items.44.size: Expected object, received string, items.45.priority: Expected object, received string, items.45.size: Expected object, received string, items.46.priority: Expected object, received string, items.47.priority: Expected object, received string, items.47.size: Expected object, received string");
