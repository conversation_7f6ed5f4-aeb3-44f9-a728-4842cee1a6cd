import {
	getActiveProjectIdCookie,
	getActiveTeamIdCookie,
	getOrganizationIdCookie,
	getTenantIdCookie
} from '@/core/lib/helpers/cookies';
import { APIService, getFallbackAPI } from '../../api.service';
import qs from 'qs';
import { GAUZY_API_BASE_SERVER_URL } from '@/core/constants/config/constants';
import { DeleteResponse, PaginationResponse } from '@/core/types/interfaces/common/data-response';
import { ITask } from '@/core/types/interfaces/task/task';
import taskSchema, { createTaskSchema, TCreateTask, TTask } from '@/core/types/schemas/task/task.schema';
import { validateApiResponse, validatePaginationResponse, ZodValidationError } from '@/core/types/schemas';

/**
 * Enhanced Task Service with Zod validation
 *
 * This service extends the base APIService to add schema validation
 * for all API responses, ensuring data integrity and type safety.
 */
class TaskService extends APIService {
	/**
	 * Fetches a single task by its ID with validation
	 *
	 * @param {string} taskId - Task identifier
	 * @returns {Promise<TTask>} - Validated task data
	 * @throws ValidationError if response data doesn't match schema
	 */
	getTaskById = async (taskId: string): Promise<TTask> => {
		try {
			const organizationId = getOrganizationIdCookie();
			const tenantId = getTenantIdCookie();

			const relations = [
				'tags',
				'teams',
				'members',
				'members.user',
				'createdByUser',
				'linkedIssues',
				'linkedIssues.taskTo',
				'linkedIssues.taskFrom',
				'parent',
				'children'
			];

			const obj = {
				'where[organizationId]': organizationId,
				'where[tenantId]': tenantId,
				'join[alias]': 'task',
				'join[leftJoinAndSelect][members]': 'task.members',
				'join[leftJoinAndSelect][user]': 'members.user',
				includeRootEpic: 'true'
			} as Record<string, string>;

			relations.forEach((rl, i) => {
				obj[`relations[${i}]`] = rl;
			});

			const query = qs.stringify(obj);

			const endpoint = GAUZY_API_BASE_SERVER_URL.value ? `/tasks/${taskId}?${query}` : `/tasks/${taskId}`;

			const response = await this.get<ITask>(endpoint);

			// Validate the response data using Zod schema
			return validateApiResponse(taskSchema, response.data, 'getTaskById API response');
		} catch (error) {
			if (error instanceof ZodValidationError) {
				this.logger.error(
					'Task by ID validation failed:',
					{
						message: error.message,
						issues: error.issues,
						taskId
					},
					'TaskService'
				);
			}
			throw error;
		}
	};

	/**
	 * Fetches a paginated list of tasks with validation
	 *
	 * @param {string} organizationId - Organization identifier
	 * @param {string} tenantId - Tenant identifier
	 * @param {string} projectId - Project identifier
	 * @param {string} teamId - Team identifier
	 * @returns {Promise<PaginationResponse<TTask>>} - Validated paginated tasks data
	 * @throws ValidationError if response data doesn't match schema
	 */
	getTasks = async (
		organizationId: string,
		tenantId: string,
		projectId: string,
		teamId: string
	): Promise<PaginationResponse<TTask>> => {
		try {
			const relations = [
				'tags',
				'teams',
				'members',
				'members.user',
				'createdByUser',
				'linkedIssues',
				'linkedIssues.taskTo',
				'linkedIssues.taskFrom',
				'parent',
				'children'
			];

			const obj = {
				'where[organizationId]': organizationId,
				'where[tenantId]': tenantId,
				'where[projectId]': projectId,
				'join[alias]': 'task',
				'join[leftJoinAndSelect][members]': 'task.members',
				'join[leftJoinAndSelect][user]': 'members.user',
				'where[teams][0]': teamId
			} as Record<string, string>;

			relations.forEach((rl, i) => {
				obj[`relations[${i}]`] = rl;
			});

			const query = qs.stringify(obj);
			const endpoint = `/tasks/team?${query}`;

			const response = await this.get<PaginationResponse<ITask>>(endpoint, { tenantId });

			// Validate the response data using Zod schema
			return validatePaginationResponse(taskSchema, response.data, 'getTasks API response');
		} catch (error) {
			if (error instanceof ZodValidationError) {
				this.logger.error(
					'Tasks validation failed:',
					{
						message: error.message,
						issues: error.issues,
						organizationId,
						tenantId,
						projectId,
						teamId
					},
					'TaskService'
				);
			}
			throw error;
		}
	};

	/**
	 * Deletes a task by its ID with validation
	 *
	 * @param {string} taskId - Task identifier
	 * @returns {Promise<DeleteResponse>} - Delete operation response
	 */
	deleteTask = async (taskId: string): Promise<DeleteResponse> => {
		try {
			const response = await this.delete<DeleteResponse>(`/tasks/${taskId}`);

			// Note: Delete operations typically return a simple response, not the deleted entity
			// So we don't validate against task schema here
			return response.data;
		} catch (error) {
			if (error instanceof ZodValidationError) {
				this.logger.error(
					'Task deletion validation failed:',
					{
						message: error.message,
						issues: error.issues,
						taskId
					},
					'TaskService'
				);
			}
			throw error;
		}
	};

	/**
	 * Updates an existing task with validation
	 *
	 * @param {string} taskId - Task identifier
	 * @param {Partial<TTask>} body - Partial task update data
	 * @returns {Promise<PaginationResponse<TTask>>} - Updated tasks list or single task response
	 * @throws ValidationError if input or response data doesn't match schema
	 */
	updateTask = async (taskId: string, body: Partial<TTask>): Promise<PaginationResponse<TTask>> => {
		try {
			// Validate input data before sending (partial validation)
			const validatedInput = validateApiResponse(
				taskSchema.partial(),
				body,
				'updateTask input data'
			) as Partial<TTask>;

			if (GAUZY_API_BASE_SERVER_URL.value) {
				const tenantId = getTenantIdCookie();
				const organizationId = getOrganizationIdCookie();
				const teamId = getActiveTeamIdCookie();
				const projectId = getActiveProjectIdCookie();

				const nBody = { ...validatedInput };
				delete nBody.selectedTeam;
				delete nBody.rootEpic;

				await this.put(`/tasks/${taskId}`, nBody);

				return this.getTasks(organizationId, tenantId, projectId, teamId);
			}

			const response = await this.put<PaginationResponse<ITask>>(`/tasks/${taskId}`, validatedInput);

			// Validate the response data
			return validatePaginationResponse(taskSchema, response.data, 'updateTask API response');
		} catch (error) {
			if (error instanceof ZodValidationError) {
				this.logger.error(
					'Task update validation failed:',
					{
						message: error.message,
						issues: error.issues,
						taskId
					},
					'TaskService'
				);
			}
			throw error;
		}
	};

	/**
	 * Creates a new task with validation
	 *
	 * @param {Partial<TCreateTask> & { title: string }} body - New task data
	 * @returns {Promise<PaginationResponse<TTask>>} - Created task or updated tasks list
	 * @throws ValidationError if input or response data doesn't match schema
	 */
	createTask = async (body: Partial<TCreateTask> & { title: string }): Promise<PaginationResponse<TTask>> => {
		try {
			if (GAUZY_API_BASE_SERVER_URL.value) {
				const organizationId = getOrganizationIdCookie();
				const teamId = getActiveTeamIdCookie();
				const tenantId = getTenantIdCookie();
				const projectId = getActiveProjectIdCookie();
				const title = body.title.trim() || '';

				const datas: TCreateTask = {
					description: '',
					teams: [
						{
							id: teamId
						}
					],
					tags: [],
					organizationId,
					tenantId,
					projectId,
					estimate: 0,
					...body,
					title // this must be called after ...body
				};

				// Validate input data before sending
				const validatedInput = validateApiResponse(
					createTaskSchema,
					datas,
					'createTask input data'
				) as TCreateTask;

				await this.post('/tasks', validatedInput, { tenantId });

				return this.getTasks(organizationId, tenantId, projectId, teamId);
			}

			const api = await getFallbackAPI();
			const response = await api.post<PaginationResponse<ITask>>('/tasks/team', body);

			// Validate the response data
			return validatePaginationResponse(taskSchema, response.data, 'createTask API response');
		} catch (error) {
			if (error instanceof ZodValidationError) {
				this.logger.error(
					'Task creation validation failed:',
					{
						message: error.message,
						issues: error.issues
					},
					'TaskService'
				);
			}
			throw error;
		}
	};

	/**
	 * Deletes an employee from tasks with validation
	 *
	 * @param {string} employeeId - Employee identifier
	 * @param {string} organizationTeamId - Organization team identifier
	 * @returns {Promise<DeleteResponse>} - Delete operation response
	 */
	deleteEmployeeFromTasks = async (employeeId: string, organizationTeamId: string): Promise<DeleteResponse> => {
		try {
			const response = await this.delete<DeleteResponse>(
				`/tasks/employee/${employeeId}?organizationTeamId=${organizationTeamId}`
			);
			return response.data;
		} catch (error) {
			if (error instanceof ZodValidationError) {
				this.logger.error(
					'Delete employee from tasks validation failed:',
					{
						message: error.message,
						issues: error.issues,
						employeeId,
						organizationTeamId
					},
					'TaskService'
				);
			}
			throw error;
		}
	};

	/**
	 * Fetches tasks by employee ID with validation
	 *
	 * @param {string} employeeId - Employee identifier
	 * @param {string} organizationTeamId - Organization team identifier
	 * @returns {Promise<TTask[]>} - Validated array of tasks
	 * @throws ValidationError if response data doesn't match schema
	 */
	getTasksByEmployeeId = async (employeeId: string, organizationTeamId: string): Promise<TTask[]> => {
		try {
			const organizationId = getOrganizationIdCookie();
			const obj = {
				'where[organizationTeamId]': organizationTeamId,
				'where[organizationId]': organizationId
			} as Record<string, string>;
			const query = qs.stringify(obj);

			const response = await this.get<ITask[]>(`/tasks/employee/${employeeId}?${query}`);

			// Validate the response data using Zod schema
			return response.data.map((task: any) =>
				validateApiResponse(taskSchema, task, 'getTasksByEmployeeId API response')
			);
		} catch (error) {
			if (error instanceof ZodValidationError) {
				this.logger.error(
					'Tasks by employee ID validation failed:',
					{
						message: error.message,
						issues: error.issues,
						employeeId,
						organizationTeamId
					},
					'TaskService'
				);
			}
			throw error;
		}
	};
}

export const taskService = new TaskService(GAUZY_API_BASE_SERVER_URL.value);



"Validation failed in getTasks API response: items.0.prefix: Expected string, received null, items.0.priority: Expected string, received null, items.0.size: Expected string, received null, items.0.version: Expected string, received null, items.0.parentId: Expected string, received null, items.0.taskSizeId: Expected string, received null, items.0.taskPriorityId: Expected string, received null, items.0.taskTypeId: Expected string, received null, items.1.prefix: Expected string, received null, items.1.priority: Expected string, received null, items.1.size: Expected string, received null, items.1.version: Expected string, received null, items.1.parentId: Expected string, received null, items.1.taskSizeId: Expected string, received null, items.1.taskPriorityId: Expected string, received null, items.1.taskTypeId: Expected string, received null, items.2.prefix: Expected string, received null, items.2.priority: Expected string, received null, items.2.size: Expected string, received null, items.2.version: Expected string, received null, items.2.parentId: Expected string, received null, items.2.taskSizeId: Expected string, received null, items.2.taskPriorityId: Expected string, received null, items.2.taskTypeId: Expected string, received null, items.3.prefix: Expected string, received null, items.3.priority: Expected string, received null, items.3.size: Expected string, received null, items.3.version: Expected string, received null, items.3.parentId: Expected string, received null, items.3.taskSizeId: Expected string, received null, items.3.taskPriorityId: Expected string, received null, items.3.taskTypeId: Expected string, received null, items.4.prefix: Expected string, received null, items.4.version: Expected string, received null, items.4.parentId: Expected string, received null, items.4.taskSizeId: Expected string, received null, items.4.taskPriorityId: Expected string, received null, items.4.taskTypeId: Expected string, received null, items.5.prefix: Expected string, received null, items.5.version: Expected string, received null, items.5.taskSizeId: Expected string, received null, items.5.taskPriorityId: Expected string, received null, items.5.taskTypeId: Expected string, received null, items.6.prefix: Expected string, received null, items.6.taskSizeId: Expected string, received null, items.6.taskPriorityId: Expected string, received null, items.6.taskTypeId: Expected string, received null, items.7.prefix: Expected string, received null, items.7.version: Expected string, received null, items.7.parentId: Expected string, received null, items.7.taskSizeId: Expected string, received null, items.7.taskPriorityId: Expected string, received null, items.7.taskTypeId: Expected string, received null, items.8.prefix: Expected string, received null, items.8.version: Expected string, received null, items.8.parentId: Expected string, received null, items.8.taskSizeId: Expected string, received null, items.8.taskPriorityId: Expected string, received null, items.8.taskTypeId: Expected string, received null, items.9.prefix: Expected string, received null, items.9.version: Expected string, received null, items.9.parentId: Expected string, received null, items.9.taskSizeId: Expected string, received null, items.9.taskPriorityId: Expected string, received null, items.9.taskTypeId: Expected string, received null, items.10.prefix: Expected string, received null, items.10.version: Expected string, received null, items.10.parentId: Expected string, received null, items.10.taskSizeId: Expected string, received null, items.10.taskPriorityId: Expected string, received null, items.10.taskTypeId: Expected string, received null, items.11.version: Expected string, received null, items.11.parentId: Expected string, received null, items.11.taskSizeId: Expected string, received null, items.11.taskPriorityId: Expected string, received null, items.11.taskTypeId: Expected string, received null, items.12.prefix: Expected string, received null, items.12.version: Expected string, received null, items.12.parentId: Expected string, received null, items.12.taskSizeId: Expected string, received null, items.12.taskPriorityId: Expected string, received null, items.12.taskTypeId: Expected string, received null, items.13.prefix: Expected string, received null, items.13.version: Expected string, received null, items.13.parentId: Expected string, received null, items.13.taskSizeId: Expected string, received null, items.13.taskPriorityId: Expected string, received null, items.13.taskTypeId: Expected string, received null, items.14.prefix: Expected string, received null, items.14.version: Expected string, received null, items.14.parentId: Expected string, received null, items.14.taskSizeId: Expected string, received null, items.14.taskPriorityId: Expected string, received null, items.14.taskTypeId: Expected string, received null, items.15.prefix: Expected string, received null, items.15.version: Expected string, received null, items.15.parentId: Expected string, received null, items.15.taskSizeId: Expected string, received null, items.15.taskPriorityId: Expected string, received null, items.15.taskTypeId: Expected string, received null, items.16.prefix: Expected string, received null, items.16.version: Expected string, received null, items.16.parentId: Expected string, received null, items.16.taskSizeId: Expected string, received null, items.16.taskPriorityId: Expected string, received null, items.16.taskTypeId: Expected string, received null, items.17.prefix: Expected string, received null, items.17.version: Expected string, received null, items.17.parentId: Expected string, received null, items.17.taskSizeId: Expected string, received null, items.17.taskPriorityId: Expected string, received null, items.17.taskTypeId: Expected string, received null, items.18.prefix: Expected string, received null, items.18.version: Expected string, received null, items.18.parentId: Expected string, received null, items.18.taskSizeId: Expected string, received null, items.18.taskPriorityId: Expected string, received null, items.18.taskTypeId: Expected string, received null, items.19.prefix: Expected string, received null, items.19.version: Expected string, received null, items.19.parentId: Expected string, received null, items.19.taskSizeId: Expected string, received null, items.19.taskPriorityId: Expected string, received null, items.19.taskTypeId: Expected string, received null, items.20.prefix: Expected string, received null, items.20.version: Expected string, received null, items.20.parentId: Expected string, received null, items.20.taskSizeId: Expected string, received null, items.20.taskPriorityId: Expected string, received null, items.20.taskTypeId: Expected string, received null, items.21.prefix: Expected string, received null, items.21.version: Expected string, received null, items.21.parentId: Expected string, received null, items.21.taskSizeId: Expected string, received null, items.21.taskPriorityId: Expected string, received null, items.21.taskTypeId: Expected string, received null, items.22.prefix: Expected string, received null, items.22.version: Expected string, received null, items.22.parentId: Expected string, received null, items.22.taskSizeId: Expected string, received null, items.22.taskPriorityId: Expected string, received null, items.22.taskTypeId: Expected string, received null, items.23.prefix: Expected string, received null, items.23.version: Expected string, received null, items.23.parentId: Expected string, received null, items.23.taskSizeId: Expected string, received null, items.23.taskPriorityId: Expected string, received null, items.23.taskTypeId: Expected string, received null, items.24.prefix: Expected string, received null, items.24.version: Expected string, received null, items.24.parentId: Expected string, received null, items.24.taskSizeId: Expected string, received null, items.24.taskPriorityId: Expected string, received null, items.24.taskTypeId: Expected string, received null, items.25.prefix: Expected string, received null, items.25.version: Expected string, received null, items.25.parentId: Expected string, received null, items.25.taskSizeId: Expected string, received null, items.25.taskPriorityId: Expected string, received null, items.25.taskTypeId: Expected string, received null, items.26.prefix: Expected string, received null, items.26.version: Expected string, received null, items.26.parentId: Expected string, received null, items.26.taskSizeId: Expected string, received null, items.26.taskPriorityId: Expected string, received null, items.26.taskTypeId: Expected string, received null, items.27.prefix: Expected string, received null, items.27.version: Expected string, received null, items.27.parentId: Expected string, received null, items.27.taskSizeId: Expected string, received null, items.27.taskPriorityId: Expected string, received null, items.27.taskTypeId: Expected string, received null, items.28.prefix: Expected string, received null, items.28.version: Expected string, received null, items.28.parentId: Expected string, received null, items.28.taskSizeId: Expected string, received null, items.28.taskPriorityId: Expected string, received null, items.28.taskTypeId: Expected string, received null, items.29.prefix: Expected string, received null, items.29.version: Expected string, received null, items.29.parentId: Expected string, received null, items.29.taskSizeId: Expected string, received null, items.29.taskPriorityId: Expected string, received null, items.29.taskTypeId: Expected string, received null, items.30.prefix: Expected string, received null, items.30.version: Expected string, received null, items.30.parentId: Expected string, received null, items.30.taskSizeId: Expected string, received null, items.30.taskPriorityId: Expected string, received null, items.30.taskTypeId: Expected string, received null, items.31.prefix: Expected string, received null, items.31.version: Expected string, received null, items.31.taskSizeId: Expected string, received null, items.31.taskPriorityId: Expected string, received null, items.31.taskTypeId: Expected string, received null, items.32.prefix: Expected string, received null, items.32.size: Expected string, received null, items.32.version: Expected string, received null, items.32.parentId: Expected string, received null, items.32.taskSizeId: Expected string, received null, items.32.taskPriorityId: Expected string, received null, items.32.taskTypeId: Expected string, received null, items.33.prefix: Expected string, received null, items.33.version: Expected string, received null, items.33.parentId: Expected string, received null, items.33.taskSizeId: Expected string, received null, items.33.taskPriorityId: Expected string, received null, items.33.taskTypeId: Expected string, received null, items.34.prefix: Expected string, received null, items.34.version: Expected string, received null, items.34.parentId: Expected string, received null, items.34.taskSizeId: Expected string, received null, items.34.taskPriorityId: Expected string, received null, items.34.taskTypeId: Expected string, received null, items.35.prefix: Expected string, received null, items.35.version: Expected string, received null, items.35.parentId: Expected string, received null, items.35.taskSizeId: Expected string, received null, items.35.taskPriorityId: Expected string, received null, items.35.taskTypeId: Expected string, received null, items.36.prefix: Expected string, received null, items.36.version: Expected string, received null, items.36.parentId: Expected string, received null, items.36.taskSizeId: Expected string, received null, items.36.taskPriorityId: Expected string, received null, items.36.taskTypeId: Expected string, received null, items.37.prefix: Expected string, received null, items.37.version: Expected string, received null, items.37.parentId: Expected string, received null, items.37.taskSizeId: Expected string, received null, items.37.taskPriorityId: Expected string, received null, items.37.taskTypeId: Expected string, received null, items.38.prefix: Expected string, received null, items.38.version: Expected string, received null, items.38.taskSizeId: Expected string, received null, items.38.taskPriorityId: Expected string, received null, items.38.taskTypeId: Expected string, received null, items.39.version: Expected string, received null, items.39.parentId: Expected string, received null, items.39.taskSizeId: Expected string, received null, items.39.taskPriorityId: Expected string, received null, items.39.taskTypeId: Expected string, received null, items.40.prefix: Expected string, received null, items.40.version: Expected string, received null, items.40.parentId: Expected string, received null, items.40.taskSizeId: Expected string, received null, items.40.taskPriorityId: Expected string, received null, items.40.taskTypeId: Expected string, received null, items.41.prefix: Expected string, received null, items.41.version: Expected string, received null, items.41.parentId: Expected string, received null, items.41.taskSizeId: Expected string, received null, items.41.taskPriorityId: Expected string, received null, items.41.taskTypeId: Expected string, received null, items.42.prefix: Expected string, received null, items.42.version: Expected string, received null, items.42.taskSizeId: Expected string, received null, items.42.taskPriorityId: Expected string, received null, items.42.taskTypeId: Expected string, received null, items.43.prefix: Expected string, received null, items.43.version: Expected string, received null, items.43.parentId: Expected string, received null, items.43.taskSizeId: Expected string, received null, items.43.taskPriorityId: Expected string, received null, items.43.taskTypeId: Expected string, received null, items.44.prefix: Expected string, received null, items.44.version: Expected string, received null, items.44.parentId: Expected string, received null, items.44.taskSizeId: Expected string, received null, items.44.taskPriorityId: Expected string, received null, items.44.taskTypeId: Expected string, received null, items.45.prefix: Expected string, received null, items.45.version: Expected string, received null, items.45.parentId: Expected string, received null, items.45.taskSizeId: Expected string, received null, items.45.taskPriorityId: Expected string, received null, items.45.taskTypeId: Expected string, received null, items.46.prefix: Expected string, received null, items.46.size: Expected string, received null, items.46.version: Expected string, received null, items.46.parentId: Expected string, received null, items.46.taskSizeId: Expected string, received null, items.46.taskPriorityId: Expected string, received null, items.46.taskTypeId: Expected string, received null, items.47.prefix: Expected string, received null, items.47.version: Expected string, received null, items.47.parentId: Expected string, received null, items.47.taskSizeId: Expected string, received null, items.47.taskPriorityId: Expected string, received null, items.47.taskTypeId: Expected string, received null"