// api service
import { EVER_TEAMS_API_BASE_URL } from '@ever-teams/constants';
import { APIService } from '../api.service';

// Import Zod validation utilities and schemas
// Note: These imports assume the schemas are available in the packages/services context
// If not available, they should be copied or re-exported from the web app
import {
	validatePaginationResponse,
	validateApiResponse,
	taskSchema,
	createTaskSchema,
	ZodValidationError,
	TTask,
	TCreateTask,
} from '../types/schemas';

/**
 * Enhanced Task Service with Zod validation
 *
 * This service extends the base APIService to add schema validation
 * for all API responses, ensuring data integrity and type safety.
 *
 * @class
 * @extends {APIService}
 */
export class TaskService extends APIService {
	/**
	 * Initializes the TaskService with a specified or default API base URL.
	 *
	 * @param {string} [baseURL] - Optional override for the default API base URL.
	 */
	constructor(baseURL: string = EVER_TEAMS_API_BASE_URL) {
		super(baseURL);
	}

	/**
	 * Fetches a paginated list of tasks with validation
	 *
	 * @param {object} [params] - Optional filters (e.g., projectId, status, search term).
	 * @returns {Promise<{items: TTask[], total: number}>} - Validated paginated tasks data
	 * @throws ValidationError if response data doesn't match schema
	 */
	async fetchTasks(params?: Record<string, any>): Promise<{ items: TTask[]; total: number }> {
		try {
			const response = await this.get<any>('/api/tasks/', { params });

			// Validate the response data using Zod schema
			return validatePaginationResponse(taskSchema, response.data, 'fetchTasks API response');
		} catch (error: any) {
			if (error instanceof ZodValidationError) {
				this.logger?.error?.(
					'Task validation failed:',
					{
						message: error.message,
						issues: error.issues,
					},
					'TaskService'
				);
			}
			throw error?.response?.data || error;
		}
	}

	/**
	 * Fetches a single task by its ID with validation
	 *
	 * @param {string} taskId - Task identifier.
	 * @returns {Promise<TTask>} - Validated task data
	 * @throws ValidationError if response data doesn't match schema
	 */
	async fetchTaskById(taskId: string): Promise<TTask> {
		try {
			const response = await this.get<any>(`/api/tasks/${taskId}/`);

			// Validate the response data using Zod schema
			return validateApiResponse(taskSchema, response.data, 'fetchTaskById API response');
		} catch (error: any) {
			if (error instanceof ZodValidationError) {
				this.logger?.error?.(
					'Task by ID validation failed:',
					{
						message: error.message,
						issues: error.issues,
						taskId,
					},
					'TaskService'
				);
			}
			throw error?.response?.data || error;
		}
	}

	/**
	 * Creates a new task with validation
	 *
	 * @param {TCreateTask} data - New task data.
	 * @returns {Promise<TTask>} - Validated created task
	 * @throws ValidationError if input or response data doesn't match schema
	 */
	async createTask(data: TCreateTask): Promise<TTask> {
		try {
			// Validate input data before sending
			const validatedInput = validateApiResponse(createTaskSchema, data, 'createTask input data');

			const response = await this.post<any>('/api/tasks/', validatedInput);

			// Validate the response data
			return validateApiResponse(taskSchema, response.data, 'createTask API response');
		} catch (error: any) {
			if (error instanceof ZodValidationError) {
				this.logger?.error?.(
					'Task creation validation failed:',
					{
						message: error.message,
						issues: error.issues,
					},
					'TaskService'
				);
			}
			throw error?.response?.data || error;
		}
	}

	/**
	 * Updates an existing task with validation
	 *
	 * @param {string} taskId - Task identifier.
	 * @param {Partial<TTask>} data - Partial task update data.
	 * @returns {Promise<TTask>} - Validated updated task
	 * @throws ValidationError if input or response data doesn't match schema
	 */
	async updateTask(taskId: string, data: Partial<TTask>): Promise<TTask> {
		try {
			// Validate input data before sending (partial validation)
			const validatedInput = validateApiResponse(taskSchema.partial(), data, 'updateTask input data');

			const response = await this.patch<any>(`/api/tasks/${taskId}/`, validatedInput);

			// Validate the response data
			return validateApiResponse(taskSchema, response.data, 'updateTask API response');
		} catch (error: any) {
			if (error instanceof ZodValidationError) {
				this.logger?.error?.(
					'Task update validation failed:',
					{
						message: error.message,
						issues: error.issues,
						taskId,
					},
					'TaskService'
				);
			}
			throw error?.response?.data || error;
		}
	}

	/**
	 * Deletes a task by its ID with validation
	 *
	 * @param {string} taskId - Task identifier.
	 * @returns {Promise<TTask>} - Validated deleted task data
	 * @throws ValidationError if response data doesn't match schema
	 */
	async deleteTask(taskId: string): Promise<TTask> {
		try {
			const response = await this.delete<any>(`/api/tasks/${taskId}/`);

			// Validate the response data (some APIs return the deleted entity)
			return validateApiResponse(taskSchema, response.data, 'deleteTask API response');
		} catch (error: any) {
			if (error instanceof ZodValidationError) {
				this.logger?.error?.(
					'Task deletion validation failed:',
					{
						message: error.message,
						issues: error.issues,
						taskId,
					},
					'TaskService'
				);
			}
			throw error?.response?.data || error;
		}
	}
}
